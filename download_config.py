"""
Configuration file for the sequential file downloader.
Modify the values below to match your specific needs.
"""

# DOWNLOAD CONFIGURATION
# ======================

# Base URL pattern - use {} where the sequential number should be inserted
# Examples:
# "https://example.com/thumb/{}.webm"
# "https://mysite.com/images/photo_{}.jpg"
# "https://domain.com/videos/clip{}.mp4"
BASE_URL = "http://wilopes.ru/2f9e917c159c1f5aeaef61f8debe0812/thumbs/{}.webp"


# File extension (without the dot)
# Examples: "webm", "mp4", "jpg", "png", "pdf"
FILE_EXTENSION = "webp"

# Range of files to download
START_NUMBER = 1
END_NUMBER = 1000

# Directory where files will be saved
OUTPUT_DIR = "downloaded_files"

# Delay between downloads in seconds (be respectful to servers)
# Recommended: 0.5 to 2.0 seconds
DELAY_SECONDS = 0.01

# Additional settings
# ===================

# Maximum number of retry attempts for failed downloads
MAX_RETRIES = 3

# Timeout for each download request (seconds)
REQUEST_TIMEOUT = 30

# Whether to skip files that already exist in the output directory
SKIP_EXISTING = True

# Whether to create subdirectories based on number ranges
# If True, files 1-100 go in "001-100/", 101-200 in "101-200/", etc.
USE_SUBDIRECTORIES = False
SUBDIRECTORY_SIZE = 100
