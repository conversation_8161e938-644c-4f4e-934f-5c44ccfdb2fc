#!/usr/bin/env python3
"""
Sequential File Downloader Script
Downloads files from a website in sequential order with customizable URL patterns.
"""

import requests
import os
import time
from urllib.parse import urlparse
import sys

class SequentialDownloader:
    def __init__(self, base_url, file_extension, start_num=1, end_num=100, 
                 output_dir="downloads", delay=0.5):
        """
        Initialize the downloader
        
        Args:
            base_url (str): Base URL pattern with {} placeholder for number
            file_extension (str): File extension (e.g., 'webm', 'mp4', 'jpg')
            start_num (int): Starting number for sequential download
            end_num (int): Ending number for sequential download
            output_dir (str): Directory to save downloaded files
            delay (float): Delay between downloads in seconds
        """
        self.base_url = base_url
        self.file_extension = file_extension
        self.start_num = start_num
        self.end_num = end_num
        self.output_dir = output_dir
        self.delay = delay
        self.session = requests.Session()
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Set up session headers to mimic a browser more completely
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': '*/*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        })
    
    def download_file(self, url, filename):
        """Download a single file with enhanced debugging"""
        try:
            print(f"Downloading: {url}")

            # Try with referrer header (common protection mechanism)
            headers = {
                'Referer': url.rsplit('/', 1)[0] + '/',  # Use directory as referrer
                'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
                'Sec-Fetch-Dest': 'image',
                'Sec-Fetch-Mode': 'no-cors',
                'Sec-Fetch-Site': 'same-origin'
            }

            response = self.session.get(url, stream=True, timeout=30, headers=headers)

            print(f"  Status Code: {response.status_code}")
            print(f"  Content-Type: {response.headers.get('content-type', 'Unknown')}")
            print(f"  Content-Length: {response.headers.get('content-length', 'Unknown')}")

            if response.status_code == 200:
                # Check if we're getting HTML instead of the expected file
                content_type = response.headers.get('content-type', '').lower()
                if 'text/html' in content_type:
                    print(f"⚠ Warning: Received HTML content instead of file for {filename}")
                    print(f"  This might be an error page or redirect")
                    # Save first 500 chars to see what we got
                    preview = response.text[:500]
                    print(f"  Content preview: {preview}")
                    return False

                filepath = os.path.join(self.output_dir, filename)
                file_size = 0

                with open(filepath, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            file_size += len(chunk)

                print(f"  Downloaded size: {file_size} bytes")

                # Check if file is suspiciously small
                if file_size < 1000:  # Less than 1KB might be an error page
                    print(f"⚠ Warning: File {filename} is very small ({file_size} bytes)")
                    print(f"  This might indicate an error or redirect")

                    # Read the file content to check if it's HTML
                    with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        if '<html' in content.lower() or '<!doctype' in content.lower():
                            print(f"  File contains HTML - likely an error page")
                            print(f"  Content preview: {content[:200]}")
                            return False

                print(f"✓ Successfully downloaded: {filename} ({file_size} bytes)")
                return True
            else:
                print(f"✗ Failed to download {filename}: HTTP {response.status_code}")
                if response.status_code == 403:
                    print(f"  403 Forbidden - Server is blocking the request")
                elif response.status_code == 404:
                    print(f"  404 Not Found - File doesn't exist at this URL")
                return False

        except requests.exceptions.RequestException as e:
            print(f"✗ Error downloading {filename}: {e}")
            return False
    
    def run(self):
        """Run the sequential download process"""
        print(f"Starting sequential download from {self.start_num} to {self.end_num}")
        print(f"Base URL: {self.base_url}")
        print(f"File extension: {self.file_extension}")
        print(f"Output directory: {self.output_dir}")
        print("-" * 50)
        
        successful_downloads = 0
        failed_downloads = 0
        
        for i in range(self.start_num, self.end_num + 1):
            # Construct the URL and filename
            url = self.base_url.format(i)
            filename = f"{i}.{self.file_extension}"
            
            # Check if file already exists
            filepath = os.path.join(self.output_dir, filename)
            if os.path.exists(filepath):
                print(f"⚠ File already exists, skipping: {filename}")
                continue
            
            # Download the file
            if self.download_file(url, filename):
                successful_downloads += 1
            else:
                failed_downloads += 1
            
            # Add delay between downloads to be respectful to the server
            if i < self.end_num:
                time.sleep(self.delay)
        
        print("-" * 50)
        print(f"Download complete!")
        print(f"Successful downloads: {successful_downloads}")
        print(f"Failed downloads: {failed_downloads}")
        print(f"Files saved to: {os.path.abspath(self.output_dir)}")


def test_url_patterns():
    """Test different URL patterns to find the correct one"""
    try:
        import download_config as config
        base_path = "http://wilopes.ru/2f9e917c159c1f5aeaef61f8debe0812/thumbs/"

        print("Testing Different URL Patterns")
        print("=" * 35)

        # Different patterns to try
        patterns = [
            f"{base_path}1.jpg",
            f"{base_path}001.jpg",
            f"{base_path}0001.jpg",
            f"{base_path}thumb_1.jpg",
            f"{base_path}thumb001.jpg",
            f"{base_path}1.webp",
            f"{base_path}1.png",
            f"{base_path}image_1.jpg",
            f"{base_path}pic1.jpg",
            f"{base_path}1",  # No extension
        ]

        downloader = SequentialDownloader(
            base_url="",  # We'll test manually
            file_extension="jpg",
            start_num=1,
            end_num=1,
            output_dir="test_patterns",
            delay=0
        )

        for i, test_url in enumerate(patterns, 1):
            print(f"\n--- Pattern {i}: {test_url} ---")
            success = downloader.download_file(test_url, f"test_pattern_{i}.jpg")
            if success:
                print(f"✓ SUCCESS! This pattern works: {test_url}")
                break
            print("✗ Failed")

    except Exception as e:
        print(f"Error during pattern test: {e}")


def test_single_url():
    """Test a single URL to debug issues"""
    try:
        import download_config as config
        test_url = config.BASE_URL.format(1)  # Test with number 1

        print("Testing Single URL")
        print("=" * 20)
        print(f"Test URL: {test_url}")
        print()

        downloader = SequentialDownloader(
            base_url=config.BASE_URL,
            file_extension=config.FILE_EXTENSION,
            start_num=1,
            end_num=1,  # Only download one file for testing
            output_dir="test_download",
            delay=0
        )

        # Test the download
        success = downloader.download_file(test_url, f"test_1.{config.FILE_EXTENSION}")

        if success:
            print("\n✓ Test successful! The URL and configuration appear to be working.")
        else:
            print("\n✗ Test failed. Check the debug output above for clues.")
            print("\nTrying different URL patterns...")
            test_url_patterns()

    except ImportError:
        print("Error: Could not load download_config.py")
    except Exception as e:
        print(f"Error during test: {e}")


def main():
    """Main function that loads configuration and runs the downloader"""

    print("Sequential File Downloader")
    print("=" * 30)

    # Check if user wants to test first
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        test_single_url()
        return

    # Try to load configuration from config file
    try:
        import download_config as config
        print("✓ Loaded configuration from download_config.py")

        BASE_URL = config.BASE_URL
        FILE_EXTENSION = config.FILE_EXTENSION
        START_NUMBER = config.START_NUMBER
        END_NUMBER = config.END_NUMBER
        OUTPUT_DIR = config.OUTPUT_DIR
        DELAY_SECONDS = config.DELAY_SECONDS

    except ImportError:
        print("⚠ Could not load download_config.py, using default values")
        print("  Create download_config.py to customize settings")

        # Default configuration
        BASE_URL = "https://example.com/thumb/{}.webm"
        FILE_EXTENSION = "webm"
        START_NUMBER = 1
        END_NUMBER = 100
        OUTPUT_DIR = "downloaded_files"
        DELAY_SECONDS = 0.5

    # Create and run the downloader
    downloader = SequentialDownloader(
        base_url=BASE_URL,
        file_extension=FILE_EXTENSION,
        start_num=START_NUMBER,
        end_num=END_NUMBER,
        output_dir=OUTPUT_DIR,
        delay=DELAY_SECONDS
    )

    try:
        downloader.run()
    except KeyboardInterrupt:
        print("\n\nDownload interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
