#!/usr/bin/env python3
"""
Sequential File Downloader Script
Downloads files from a website in sequential order with customizable URL patterns.
"""

import requests
import os
import time
from urllib.parse import urlparse
import sys

class SequentialDownloader:
    def __init__(self, base_url, file_extension, start_num=1, end_num=100, 
                 output_dir="downloads", delay=0.5):
        """
        Initialize the downloader
        
        Args:
            base_url (str): Base URL pattern with {} placeholder for number
            file_extension (str): File extension (e.g., 'webm', 'mp4', 'jpg')
            start_num (int): Starting number for sequential download
            end_num (int): Ending number for sequential download
            output_dir (str): Directory to save downloaded files
            delay (float): Delay between downloads in seconds
        """
        self.base_url = base_url
        self.file_extension = file_extension
        self.start_num = start_num
        self.end_num = end_num
        self.output_dir = output_dir
        self.delay = delay
        self.session = requests.Session()
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Set up session headers to exactly match working Firefox browser
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'DNT': '1'
        })

        # Disable redirects by default to avoid ad redirects
        self.follow_redirects = False
    
    def establish_session(self):
        """Set up session with exact cookies from working browser"""
        try:
            # Import cookies from config
            import download_config as config

            print("Setting up session with browser cookies...")

            # Set the exact cookies from the working browser session
            if hasattr(config, 'COOKIES'):
                for name, value in config.COOKIES.items():
                    self.session.cookies.set(name, value, domain='wilopes.ru')
                print(f"✓ Set {len(config.COOKIES)} cookies from config")

                # Print cookies for verification
                for name, value in config.COOKIES.items():
                    print(f"  {name}={value[:20]}...")
            else:
                print("⚠ No COOKIES found in config - using session establishment")

                # Fallback to visiting main site
                main_site = "http://wilopes.ru/"
                print(f"Visiting: {main_site}")
                response = self.session.get(main_site, timeout=30)
                print(f"Response: {response.status_code}")

        except Exception as e:
            print(f"Warning: Could not establish session: {e}")

    def download_file(self, url, filename):
        """Download a single file with enhanced anti-hotlinking protection bypass"""
        try:
            print(f"Downloading: {url}")

            # First establish session if this is the first download
            if not hasattr(self, '_session_established'):
                self.establish_session()
                self._session_established = True

            # Use EXACT headers from working browser request
            headers = {
                'Accept': 'image/avif,image/webp,image/png,image/svg+xml,image/*;q=0.8,*/*;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Accept-Language': 'en-US,en;q=0.5',
                'Connection': 'keep-alive',
                'Host': 'wilopes.ru',
                'Priority': 'u=4, i',
                'Referer': 'http://wilopes.ru/',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0'
            }

            # Try without following redirects first
            response = self.session.get(url, stream=True, timeout=30, headers=headers, allow_redirects=False)

            print(f"  Status Code: {response.status_code}")
            print(f"  Final URL: {response.url}")
            print(f"  Content-Type: {response.headers.get('content-type', 'Unknown')}")
            print(f"  Content-Length: {response.headers.get('content-length', 'Unknown')}")

            # Handle different response codes
            if response.status_code == 302 or response.status_code == 301:
                print(f"  Redirect detected to: {response.headers.get('location', 'Unknown')}")
                redirect_url = response.headers.get('location', '')

                # Check if redirect is to an ad domain (common with hotlinking protection)
                if 'cadrctlnk' in redirect_url or 'ad' in redirect_url.lower():
                    print(f"  ⚠ Detected ad redirect - trying alternative approach")

                    # Try with different approach - simulate clicking from the main page
                    time.sleep(1)  # Brief delay
                    headers['Referer'] = 'http://wilopes.ru/'
                    response = self.session.get(url, stream=True, timeout=30, headers=headers, allow_redirects=False)
                    print(f"  Retry Status Code: {response.status_code}")

                    if response.status_code in [301, 302]:
                        print(f"  Still redirecting - this URL may not contain actual files")
                        return False

            if response.status_code == 200:
                # Check if we're getting HTML instead of the expected file
                content_type = response.headers.get('content-type', '').lower()
                if 'text/html' in content_type:
                    print(f"⚠ Warning: Received HTML content instead of file for {filename}")
                    print(f"  This might be an error page or redirect")
                    # Save first 500 chars to see what we got
                    preview = response.text[:500]
                    print(f"  Content preview: {preview}")
                    return False

                filepath = os.path.join(self.output_dir, filename)
                file_size = 0

                with open(filepath, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            file_size += len(chunk)

                print(f"  Downloaded size: {file_size} bytes")

                # Check if file is suspiciously small
                if file_size < 1000:  # Less than 1KB might be an error page
                    print(f"⚠ Warning: File {filename} is very small ({file_size} bytes)")
                    print(f"  This might indicate an error or redirect")

                    # Read the file content to check if it's HTML
                    with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        if '<html' in content.lower() or '<!doctype' in content.lower():
                            print(f"  File contains HTML - likely an error page")
                            print(f"  Content preview: {content[:200]}")
                            return False

                print(f"✓ Successfully downloaded: {filename} ({file_size} bytes)")
                return True
            else:
                print(f"✗ Failed to download {filename}: HTTP {response.status_code}")
                if response.status_code == 403:
                    print(f"  403 Forbidden - Server is blocking the request")
                elif response.status_code == 404:
                    print(f"  404 Not Found - File doesn't exist at this URL")
                return False

        except requests.exceptions.RequestException as e:
            print(f"✗ Error downloading {filename}: {e}")
            return False
    
    def run(self):
        """Run the sequential download process"""
        print(f"Starting sequential download from {self.start_num} to {self.end_num}")
        print(f"Base URL: {self.base_url}")
        print(f"File extension: {self.file_extension}")
        print(f"Output directory: {self.output_dir}")
        print("-" * 50)
        
        successful_downloads = 0
        failed_downloads = 0
        
        for i in range(self.start_num, self.end_num + 1):
            # Construct the URL and filename
            url = self.base_url.format(i)
            filename = f"{i}.{self.file_extension}"
            
            # Check if file already exists
            filepath = os.path.join(self.output_dir, filename)
            if os.path.exists(filepath):
                print(f"⚠ File already exists, skipping: {filename}")
                continue
            
            # Download the file
            if self.download_file(url, filename):
                successful_downloads += 1
            else:
                failed_downloads += 1
            
            # Add delay between downloads to be respectful to the server
            if i < self.end_num:
                time.sleep(self.delay)
        
        print("-" * 50)
        print(f"Download complete!")
        print(f"Successful downloads: {successful_downloads}")
        print(f"Failed downloads: {failed_downloads}")
        print(f"Files saved to: {os.path.abspath(self.output_dir)}")


def test_redirect_behavior():
    """Test what happens with redirects disabled"""
    try:
        import download_config as config
        test_url = config.BASE_URL.format(1)

        print("Testing Redirect Behavior")
        print("=" * 25)
        print(f"Test URL: {test_url}")
        print()

        # Test with redirects disabled
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Referer': test_url.rsplit('/', 1)[0] + '/',
        })

        print("--- Testing WITHOUT following redirects ---")
        response = session.get(test_url, allow_redirects=False, timeout=30)
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")

        if 'location' in response.headers:
            redirect_url = response.headers['location']
            print(f"Redirect Location: {redirect_url}")

            print("\n--- Testing the redirect URL ---")
            redirect_response = session.get(redirect_url, timeout=30)
            print(f"Redirect Status: {redirect_response.status_code}")
            print(f"Redirect Content-Type: {redirect_response.headers.get('content-type', 'Unknown')}")
            print(f"Redirect Content-Length: {redirect_response.headers.get('content-length', 'Unknown')}")

    except Exception as e:
        print(f"Error during redirect test: {e}")


def test_url_patterns():
    """Test different URL patterns to find the correct one"""
    try:
        base_path = "http://wilopes.ru/2f9e917c159c1f5aeaef61f8debe0812/thumbs/"

        print("Testing Different URL Patterns")
        print("=" * 35)

        # Different patterns to try
        patterns = [
            f"{base_path}1.jpg",
            f"{base_path}001.jpg",
            f"{base_path}0001.jpg",
            f"{base_path}thumb_1.jpg",
            f"{base_path}thumb001.jpg",
            f"{base_path}1.webp",
            f"{base_path}1.png",
            f"{base_path}image_1.jpg",
            f"{base_path}pic1.jpg",
            f"{base_path}1",  # No extension
        ]

        downloader = SequentialDownloader(
            base_url="",  # We'll test manually
            file_extension="jpg",
            start_num=1,
            end_num=1,
            output_dir="test_patterns",
            delay=0
        )

        for i, test_url in enumerate(patterns, 1):
            print(f"\n--- Pattern {i}: {test_url} ---")
            success = downloader.download_file(test_url, f"test_pattern_{i}.jpg")
            if success:
                print(f"✓ SUCCESS! This pattern works: {test_url}")
                break
            print("✗ Failed")

    except Exception as e:
        print(f"Error during pattern test: {e}")


def test_single_url():
    """Test a single URL to debug issues"""
    try:
        import download_config as config
        test_url = config.BASE_URL.format(1)  # Test with number 1

        print("Testing Single URL")
        print("=" * 20)
        print(f"Test URL: {test_url}")
        print()

        downloader = SequentialDownloader(
            base_url=config.BASE_URL,
            file_extension=config.FILE_EXTENSION,
            start_num=1,
            end_num=1,  # Only download one file for testing
            output_dir="test_download",
            delay=0
        )

        # Test the download
        success = downloader.download_file(test_url, f"test_1.{config.FILE_EXTENSION}")

        if success:
            print("\n✓ Test successful! The URL and configuration appear to be working.")
        else:
            print("\n✗ Test failed. Check the debug output above for clues.")
            print("\n" + "="*50)
            test_redirect_behavior()
            print("\n" + "="*50)
            print("Trying different URL patterns...")
            test_url_patterns()

    except ImportError:
        print("Error: Could not load download_config.py")
    except Exception as e:
        print(f"Error during test: {e}")


def main():
    """Main function that loads configuration and runs the downloader"""

    print("Sequential File Downloader")
    print("=" * 30)

    # Check if user wants to test first
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        test_single_url()
        return

    # Try to load configuration from config file
    try:
        import download_config as config
        print("✓ Loaded configuration from download_config.py")

        BASE_URL = config.BASE_URL
        FILE_EXTENSION = config.FILE_EXTENSION
        START_NUMBER = config.START_NUMBER
        END_NUMBER = config.END_NUMBER
        OUTPUT_DIR = config.OUTPUT_DIR
        DELAY_SECONDS = config.DELAY_SECONDS

    except ImportError:
        print("⚠ Could not load download_config.py, using default values")
        print("  Create download_config.py to customize settings")

        # Default configuration
        BASE_URL = "https://example.com/thumb/{}.webm"
        FILE_EXTENSION = "webm"
        START_NUMBER = 1
        END_NUMBER = 100
        OUTPUT_DIR = "downloaded_files"
        DELAY_SECONDS = 0.5

    # Create and run the downloader
    downloader = SequentialDownloader(
        base_url=BASE_URL,
        file_extension=FILE_EXTENSION,
        start_num=START_NUMBER,
        end_num=END_NUMBER,
        output_dir=OUTPUT_DIR,
        delay=DELAY_SECONDS
    )

    try:
        downloader.run()
    except KeyboardInterrupt:
        print("\n\nDownload interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
