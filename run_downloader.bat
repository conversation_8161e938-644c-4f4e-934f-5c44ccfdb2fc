@echo off
echo Sequential File Downloader
echo ==========================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

REM Check if requests library is installed
python -c "import requests" >nul 2>&1
if errorlevel 1 (
    echo Installing required Python package: requests
    pip install requests
    if errorlevel 1 (
        echo ERROR: Failed to install requests package
        pause
        exit /b 1
    )
)

echo Starting download...
echo.
python file_downloader.py

echo.
echo Download process completed.
pause
