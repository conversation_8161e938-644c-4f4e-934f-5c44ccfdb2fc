#!/usr/bin/env python3
"""
Try to find the real URLs by examining the main page or directory listing
"""

import requests
from bs4 import BeautifulSoup
import re

def explore_site():
    """Explore the site to find real file URLs"""
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
    })
    
    print("=== EXPLORING SITE STRUCTURE ===")
    
    # Try the main site
    print("\n--- Main site ---")
    try:
        response = session.get("http://wilopes.ru/", timeout=10)
        print(f"Status: {response.status_code}")
        print(f"Content-Type: {response.headers.get('content-type', 'Unknown')}")
        
        if 'text/html' in response.headers.get('content-type', ''):
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Look for any image URLs or file references
            images = soup.find_all('img')
            print(f"Found {len(images)} img tags")
            
            links = soup.find_all('a')
            print(f"Found {len(links)} links")
            
            # Look for any URLs that might contain our hash
            hash_pattern = "2f9e917c159c1f5aeaef61f8debe0812"
            if hash_pattern in response.text:
                print(f"✓ Found hash {hash_pattern} in main page")
                # Extract URLs containing the hash
                urls = re.findall(r'https?://[^\s"\'<>]+' + hash_pattern + r'[^\s"\'<>]*', response.text)
                for url in urls[:5]:  # Show first 5
                    print(f"  Found URL: {url}")
            else:
                print(f"✗ Hash {hash_pattern} not found in main page")
    
    except Exception as e:
        print(f"Error accessing main site: {e}")
    
    # Try the directory path
    print("\n--- Directory path ---")
    try:
        dir_url = "http://wilopes.ru/2f9e917c159c1f5aeaef61f8debe0812/"
        response = session.get(dir_url, timeout=10)
        print(f"Directory status: {response.status_code}")
        print(f"Content-Type: {response.headers.get('content-type', 'Unknown')}")
        
        if response.status_code == 200 and 'text/html' in response.headers.get('content-type', ''):
            print("Directory listing found!")
            print(f"Content preview: {response.text[:500]}")
            
            # Look for file links
            soup = BeautifulSoup(response.text, 'html.parser')
            links = soup.find_all('a')
            webp_links = [link.get('href') for link in links if link.get('href') and '.webp' in link.get('href')]
            
            if webp_links:
                print(f"Found {len(webp_links)} .webp links:")
                for link in webp_links[:10]:  # Show first 10
                    print(f"  {link}")
            else:
                print("No .webp links found in directory")
    
    except Exception as e:
        print(f"Error accessing directory: {e}")
    
    # Try the thumbs directory
    print("\n--- Thumbs directory ---")
    try:
        thumbs_url = "http://wilopes.ru/2f9e917c159c1f5aeaef61f8debe0812/thumbs/"
        response = session.get(thumbs_url, timeout=10)
        print(f"Thumbs status: {response.status_code}")
        print(f"Content-Type: {response.headers.get('content-type', 'Unknown')}")
        
        if response.status_code == 200:
            print(f"Content preview: {response.text[:500]}")
            
            if 'text/html' in response.headers.get('content-type', ''):
                soup = BeautifulSoup(response.text, 'html.parser')
                links = soup.find_all('a')
                file_links = [link.get('href') for link in links if link.get('href') and ('.' in link.get('href'))]
                
                if file_links:
                    print(f"Found {len(file_links)} file links:")
                    for link in file_links[:10]:
                        print(f"  {link}")
                        
                        # Try to construct full URL and test it
                        if not link.startswith('http'):
                            full_url = thumbs_url + link
                            print(f"    Testing: {full_url}")
                            try:
                                test_response = session.head(full_url, timeout=5)
                                print(f"    Status: {test_response.status_code}, Type: {test_response.headers.get('content-type', 'Unknown')}")
                            except:
                                print(f"    Failed to test")
    
    except Exception as e:
        print(f"Error accessing thumbs directory: {e}")

if __name__ == "__main__":
    explore_site()
