#!/usr/bin/env python3
"""
Simple test to debug the exact issue with downloading
"""

import requests
import time

def test_direct_download():
    """Test downloading the file that works in browser"""
    
    url = "http://wilopes.ru/2f9e917c159c1f5aeaef61f8debe0812/thumbs/117.webp"
    
    print("=== SIMPLE DIRECT TEST ===")
    print(f"URL: {url}")
    print()
    
    # Test 1: Minimal request
    print("--- Test 1: Minimal request ---")
    try:
        response = requests.get(url, allow_redirects=False, timeout=10)
        print(f"Status: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        if response.status_code in [301, 302]:
            print(f"Redirect to: {response.headers.get('location', 'Unknown')}")
    except Exception as e:
        print(f"Error: {e}")
    
    print()
    
    # Test 2: With basic browser headers
    print("--- Test 2: With browser headers ---")
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    }
    
    try:
        response = requests.get(url, headers=headers, allow_redirects=False, timeout=10)
        print(f"Status: {response.status_code}")
        print(f"Content-Type: {response.headers.get('content-type', 'Unknown')}")
        print(f"Content-Length: {response.headers.get('content-length', 'Unknown')}")
        if response.status_code in [301, 302]:
            print(f"Redirect to: {response.headers.get('location', 'Unknown')}")
    except Exception as e:
        print(f"Error: {e}")
    
    print()
    
    # Test 3: With full browser simulation
    print("--- Test 3: Full browser simulation ---")
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    })
    
    try:
        # First visit main site
        print("Visiting main site...")
        main_response = session.get("http://wilopes.ru/", timeout=10)
        print(f"Main site status: {main_response.status_code}")
        
        time.sleep(1)
        
        # Then try the file
        print("Trying file download...")
        file_headers = {
            'Referer': 'http://wilopes.ru/',
            'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
        }
        
        response = session.get(url, headers=file_headers, allow_redirects=False, timeout=10)
        print(f"Status: {response.status_code}")
        print(f"Content-Type: {response.headers.get('content-type', 'Unknown')}")
        print(f"Content-Length: {response.headers.get('content-length', 'Unknown')}")
        
        if response.status_code in [301, 302]:
            print(f"Redirect to: {response.headers.get('location', 'Unknown')}")
        elif response.status_code == 200:
            content_type = response.headers.get('content-type', '').lower()
            if 'image' in content_type:
                print("✓ SUCCESS! Got actual image content")
                with open('test_117.webp', 'wb') as f:
                    f.write(response.content)
                print(f"Saved test file: {len(response.content)} bytes")
            else:
                print(f"⚠ Got non-image content: {content_type}")
                print(f"Content preview: {response.text[:200]}")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_direct_download()
