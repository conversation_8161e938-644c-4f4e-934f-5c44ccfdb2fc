#!/usr/bin/env python3
"""
Simple test to debug the exact issue with downloading
"""

import requests
import time

def test_with_cookies():
    """Test with exact cookies from working browser"""

    print("=== TESTING WITH BROWSER COOKIES ===")
    print()

    # Test the new URL you provided
    test_url = "http://kilodazz.ru/d145dd4bfffbdddabec0b95d5c4f69dc/thumbs/190.webp"
    print(f"Testing: {test_url}")

    # Also test a few numbers around 190
    test_files = [188, 189, 190, 191, 192]
    base_url = "http://kilodazz.ru/d145dd4bfffbdddabec0b95d5c4f69dc/thumbs/"

    # Exact cookies from working browser
    cookies = {
        'PHPSESSID': 'mdaucbfnj00r8mjceklctevqvr',
        'grece': 'url_files',
        'stybe': 'success',
        'theme': 'slate',
        'time': '1752521221',
        'domain': 'wilopes.ru',
        'ftt2': 'eyJpcCI6MTI3NzMzMjgzOSwiZiI6IjY1ODEiLCJzIjoibm9ybWFsIiwidiI6eyIwIjoiODc5MyIsIjEiOiI3MjUwIiwiMiI6NzcxMSwiMyI6Ijc4NTAiLCI0IjoiODU3NCIsIjUiOiI4NTgzIiwiMTIiOiI4NDg0In0sImNjIjoiMCIsImluIjoxfQ==',
        'ftt2_first': '1'
    }

    # Exact headers from working browser
    headers = {
        'Accept': 'image/avif,image/webp,image/png,image/svg+xml,image/*;q=0.8,*/*;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Accept-Language': 'en-US,en;q=0.5',
        'Connection': 'keep-alive',
        'Host': 'wilopes.ru',
        'Priority': 'u=4, i',
        'Referer': 'http://wilopes.ru/',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0'
    }

    print("--- Testing multiple files with exact browser cookies and headers ---")

    for file_num in test_files:
        url = f"{base_url}{file_num}.webp"
        print(f"\nTesting file {file_num}: {url}")

        try:
            response = requests.get(url, headers=headers, cookies=cookies, timeout=10)
            print(f"  Status: {response.status_code}")
            print(f"  Content-Type: {response.headers.get('content-type', 'Unknown')}")
            print(f"  Content-Length: {response.headers.get('content-length', 'Unknown')}")

            if response.status_code == 200:
                content_type = response.headers.get('content-type', '').lower()
                if 'image' in content_type:
                    print(f"  ✓ SUCCESS! Got actual image content")
                    filename = f'test_{file_num}_cookies.webp'
                    with open(filename, 'wb') as f:
                        f.write(response.content)
                    print(f"  Saved: {filename} ({len(response.content)} bytes)")
                    return True
                else:
                    print(f"  ⚠ Got non-image content: {content_type}")
                    print(f"  Content preview: {response.text[:100]}")
            elif response.status_code == 404:
                print(f"  ✗ File not found (404)")
            elif response.status_code in [301, 302]:
                print(f"  ✗ Redirect to: {response.headers.get('location', 'Unknown')}")
            else:
                print(f"  ✗ Failed with status {response.status_code}")

        except Exception as e:
            print(f"  Error: {e}")

    print(f"\n✗ None of the tested files were successfully downloaded")
    print("This could mean:")
    print("- The cookies have expired")
    print("- The file numbering scheme is different")
    print("- The files don't exist at these URLs")
    return False

if __name__ == "__main__":
    test_with_cookies()
