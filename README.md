# Sequential File Downloader

A Python script to download files from a website in sequential order (1.webm, 2.webm, 3.webm, etc.).

## Quick Start

1. **Edit the configuration**: Open `download_config.py` and modify the settings:
   ```python
   BASE_URL = "https://example.com/thumb/{}.webm"  # Your URL pattern
   FILE_EXTENSION = "webm"                         # File type
   START_NUMBER = 1                                # Start from this number
   END_NUMBER = 100                                # End at this number
   OUTPUT_DIR = "downloaded_files"                 # Where to save files
   ```

2. **Run the downloader**:
   - **Windows**: Double-click `run_downloader.bat`
   - **Command line**: `python file_downloader.py`

## Configuration Options

Edit `download_config.py` to customize:

- `BASE_URL`: URL pattern with `{}` where the number goes
  - Example: `"https://mysite.com/thumb/{}.webm"`
- `FILE_EXTENSION`: File extension without the dot
  - Examples: `"webm"`, `"mp4"`, `"jpg"`, `"png"`
- `START_NUMBER` / `END_NUMBER`: Range of files to download
- `OUTPUT_DIR`: Directory where files will be saved
- `DELAY_SECONDS`: Pause between downloads (be respectful to servers)

## URL Pattern Examples

```python
# For URLs like: example.com/thumb/1.webm, example.com/thumb/2.webm
BASE_URL = "https://example.com/thumb/{}.webm"

# For URLs like: mysite.com/images/photo_001.jpg, mysite.com/images/photo_002.jpg
BASE_URL = "https://mysite.com/images/photo_{:03d}.jpg"  # Adds zero padding

# For URLs like: domain.com/files/document1.pdf, domain.com/files/document2.pdf
BASE_URL = "https://domain.com/files/document{}.pdf"
```

## Features

- ✅ Sequential downloading with customizable number ranges
- ✅ Automatic retry for failed downloads
- ✅ Skip files that already exist
- ✅ Progress tracking and error reporting
- ✅ Respectful delays between requests
- ✅ Browser-like headers to avoid blocking
- ✅ Easy configuration through separate config file

## Requirements

- Python 3.6+
- `requests` library (automatically installed by run_downloader.bat)

## Manual Installation

If you prefer to install dependencies manually:

```bash
pip install requests
python file_downloader.py
```

## Troubleshooting

- **403/404 errors**: Check if the URL pattern is correct
- **Slow downloads**: Increase `DELAY_SECONDS` to be more respectful to the server
- **Python not found**: Make sure Python is installed and in your PATH
- **Permission errors**: Run as administrator or check folder permissions

## Example Output

```
Sequential File Downloader
==============================
✓ Loaded configuration from download_config.py
Starting sequential download from 1 to 100
Base URL: https://example.com/thumb/{}.webm
File extension: webm
Output directory: downloaded_files
--------------------------------------------------
Downloading: https://example.com/thumb/1.webm
✓ Successfully downloaded: 1.webm
Downloading: https://example.com/thumb/2.webm
✓ Successfully downloaded: 2.webm
...
--------------------------------------------------
Download complete!
Successful downloads: 98
Failed downloads: 2
Files saved to: C:\a\downloaded_files
```
